import { db } from "../lib/db.js";
import { logger, LogErrorStack } from "../utils/log.js";
import type { game_config } from "@prisma/client";

export interface GameConfigCreateInput {
    key: string;
    value: unknown;
    section: string;
    isPublic?: boolean;
    description?: string;
    version?: number;
}

export interface GameConfigUpdateInput {
    value?: unknown;
    section?: string;
    isPublic?: boolean;
    description?: string;
    version?: number;
}

/**
 * Get all game config entries
 */
export const getAllConfigs = async (): Promise<game_config[]> => {
    try {
        return await db.game_config.findMany({
            orderBy: [{ section: "asc" }, { key: "asc" }],
        });
    } catch (error) {
        LogErrorStack("Failed to get all game configs", error);
        throw error;
    }
};

/**
 * Get all public game config entries
 */
export const getPublicConfigs = async (): Promise<game_config[]> => {
    try {
        return await db.game_config.findMany({
            where: { isPublic: true },
            orderBy: [{ section: "asc" }, { key: "asc" }],
        });
    } catch (error) {
        LogErrorStack("Failed to get public game configs", error);
        throw error;
    }
};

/**
 * Get game config entries by section
 */
export const getConfigsBySection = async (section: string): Promise<game_config[]> => {
    try {
        return await db.game_config.findMany({
            where: { section },
            orderBy: { key: "asc" },
        });
    } catch (error) {
        LogErrorStack(`Failed to get game configs for section: ${section}`, error);
        throw error;
    }
};

/**
 * Get a specific game config by key
 */
export const getConfigByKey = async (key: string): Promise<game_config | null> => {
    try {
        return await db.game_config.findUnique({
            where: { key },
        });
    } catch (error) {
        LogErrorStack(`Failed to get game config for key: ${key}`, error);
        throw error;
    }
};

/**
 * Create a new game config entry
 */
export const createConfig = async (data: GameConfigCreateInput): Promise<game_config> => {
    try {
        return await db.game_config.create({
            data: {
                key: data.key,
                value: data.value,
                section: data.section,
                isPublic: data.isPublic ?? true,
                description: data.description,
                version: data.version ?? 1.0,
            },
        });
    } catch (error) {
        LogErrorStack(`Failed to create game config for key: ${data.key}`, error);
        throw error;
    }
};

/**
 * Update an existing game config entry
 */
export const updateConfig = async (key: string, data: GameConfigUpdateInput): Promise<game_config> => {
    try {
        return await db.game_config.update({
            where: { key },
            data: {
                value: data.value,
                section: data.section,
                isPublic: data.isPublic,
                description: data.description,
                version: data.version,
            },
        });
    } catch (error) {
        LogErrorStack(`Failed to update game config for key: ${key}`, error);
        throw error;
    }
};

/**
 * Upsert a game config entry (create if not exists, update if exists)
 */
export const upsertConfig = async (data: GameConfigCreateInput): Promise<game_config> => {
    try {
        return await db.game_config.upsert({
            where: { key: data.key },
            create: {
                key: data.key,
                value: data.value,
                section: data.section,
                isPublic: data.isPublic ?? true,
                description: data.description,
                version: data.version ?? 1.0,
            },
            update: {
                value: data.value,
                section: data.section,
                isPublic: data.isPublic,
                description: data.description,
                version: data.version,
            },
        });
    } catch (error) {
        LogErrorStack(`Failed to upsert game config for key: ${data.key}`, error);
        throw error;
    }
};

/**
 * Delete a game config entry
 */
export const deleteConfig = async (key: string): Promise<game_config> => {
    try {
        return await db.game_config.delete({
            where: { key },
        });
    } catch (error) {
        LogErrorStack(`Failed to delete game config for key: ${key}`, error);
        throw error;
    }
};

/**
 * Delete all game config entries (useful for resetting)
 */
export const deleteAllConfigs = async (): Promise<{ count: number }> => {
    try {
        return await db.game_config.deleteMany({});
    } catch (error) {
        LogErrorStack("Failed to delete all game configs", error);
        throw error;
    }
};

/**
 * Get the current config version
 */
export const getCurrentVersion = async (): Promise<number> => {
    try {
        const result = await db.game_config.aggregate({
            _max: {
                version: true,
            },
        });
        return result._max.version ?? 1.0;
    } catch (error) {
        LogErrorStack("Failed to get current config version", error);
        throw error;
    }
};

/**
 * Bulk upsert multiple config entries
 */
export const bulkUpsertConfigs = async (configs: GameConfigCreateInput[]): Promise<void> => {
    try {
        await db.$transaction(
            configs.map((config) =>
                db.game_config.upsert({
                    where: { key: config.key },
                    create: {
                        key: config.key,
                        value: config.value,
                        section: config.section,
                        isPublic: config.isPublic ?? true,
                        description: config.description,
                        version: config.version ?? 1.0,
                    },
                    update: {
                        value: config.value,
                        section: config.section,
                        isPublic: config.isPublic,
                        description: config.description,
                        version: config.version,
                    },
                })
            )
        );
    } catch (error) {
        LogErrorStack("Failed to bulk upsert game configs", error);
        throw error;
    }
};
