#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to seed the database with game configuration values from static config
 * Usage: npm run seed:config
 */

import { db } from "../lib/db.js";
import * as GameConfigService from "../core/gameconfig.service.js";
import { logger } from "../utils/log.js";

async function seedGameConfig() {
    try {
        logger.info("Starting game config seeding...");

        // Check if config already exists
        const existingConfigs = await db.game_config.count();
        
        if (existingConfigs > 0) {
            logger.info(`Found ${existingConfigs} existing config entries`);
            const answer = process.argv.includes("--force") ? "y" : await promptUser("Config entries already exist. Overwrite? (y/N): ");
            
            if (answer.toLowerCase() !== "y" && answer.toLowerCase() !== "yes") {
                logger.info("Seeding cancelled");
                return;
            }
        }

        // Seed from static config
        await GameConfigService.seedFromStaticConfig();
        
        // Verify seeding
        const finalCount = await db.game_config.count();
        logger.info(`Seeding completed successfully. Total config entries: ${finalCount}`);

    } catch (error) {
        logger.error("Failed to seed game config:", error);
        process.exit(1);
    } finally {
        await db.$disconnect();
    }
}

function promptUser(question: string): Promise<string> {
    return new Promise((resolve) => {
        const readline = require("readline");
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
        });

        rl.question(question, (answer: string) => {
            rl.close();
            resolve(answer);
        });
    });
}

// Run the seeding if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    seedGameConfig();
}

export { seedGameConfig };
