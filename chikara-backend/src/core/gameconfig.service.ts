import { gameConfig as staticGameConfig, publicConfig as staticPublicConfig } from "../config/gameConfig.js";
import * as GameConfigRepository from "../repositories/gameconfig.repository.js";
import { logger, LogErrorStack } from "../utils/log.js";
import type { game_config } from "@prisma/client";

// In-memory cache for config values
let configCache: Map<string, unknown> = new Map();
let publicConfigCache: Map<string, unknown> = new Map();
let cacheLastUpdated: Date | null = null;
const CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes

/**
 * Check if cache is valid
 */
const isCacheValid = (): boolean => {
    if (!cacheLastUpdated) return false;
    return Date.now() - cacheLastUpdated.getTime() < CACHE_TTL_MS;
};

/**
 * Refresh the config cache from database
 */
const refreshCache = async (): Promise<void> => {
    try {
        const allConfigs = await GameConfigRepository.getAllConfigs();
        const publicConfigs = allConfigs.filter((config) => config.isPublic);

        // Clear existing caches
        configCache.clear();
        publicConfigCache.clear();

        // Populate caches
        for (const config of allConfigs) {
            configCache.set(config.key, config.value);
        }

        for (const config of publicConfigs) {
            publicConfigCache.set(config.key, config.value);
        }

        cacheLastUpdated = new Date();
        logger.info(`Game config cache refreshed with ${allConfigs.length} entries`);
    } catch (error) {
        LogErrorStack("Failed to refresh game config cache", error);
        // Don't throw - we'll fall back to static config
    }
};

/**
 * Get a config value with fallback to static config
 */
export const getConfig = async <T = unknown>(key: string): Promise<T> => {
    try {
        // Check cache first
        if (!isCacheValid()) {
            await refreshCache();
        }

        // Try to get from cache
        if (configCache.has(key)) {
            return configCache.get(key) as T;
        }

        // Fallback to static config
        const flatStaticConfig = staticGameConfig as Record<string, unknown>;
        if (key in flatStaticConfig) {
            return flatStaticConfig[key] as T;
        }

        throw new Error(`Config key '${key}' not found in database or static config`);
    } catch (error) {
        LogErrorStack(`Failed to get config for key: ${key}`, error);
        
        // Final fallback to static config
        const flatStaticConfig = staticGameConfig as Record<string, unknown>;
        if (key in flatStaticConfig) {
            return flatStaticConfig[key] as T;
        }

        throw error;
    }
};

/**
 * Get all config values (admin only)
 */
export const getAllConfig = async (): Promise<Record<string, unknown>> => {
    try {
        if (!isCacheValid()) {
            await refreshCache();
        }

        // Merge database config with static config (database takes precedence)
        const result: Record<string, unknown> = { ...staticGameConfig };
        
        for (const [key, value] of configCache.entries()) {
            result[key] = value;
        }

        return result;
    } catch (error) {
        LogErrorStack("Failed to get all config", error);
        // Fallback to static config
        return { ...staticGameConfig };
    }
};

/**
 * Get public config values
 */
export const getPublicConfig = async (): Promise<Record<string, unknown>> => {
    try {
        if (!isCacheValid()) {
            await refreshCache();
        }

        // Merge database public config with static public config (database takes precedence)
        const result: Record<string, unknown> = { ...staticPublicConfig };
        
        for (const [key, value] of publicConfigCache.entries()) {
            result[key] = value;
        }

        return result;
    } catch (error) {
        LogErrorStack("Failed to get public config", error);
        // Fallback to static public config
        return { ...staticPublicConfig };
    }
};

/**
 * Update a config value in the database
 */
export const updateConfig = async (
    key: string,
    value: unknown,
    section?: string,
    isPublic?: boolean,
    description?: string
): Promise<void> => {
    try {
        await GameConfigRepository.upsertConfig({
            key,
            value,
            section: section ?? "general",
            isPublic: isPublic ?? true,
            description,
            version: staticGameConfig.version,
        });

        // Invalidate cache to force refresh on next access
        cacheLastUpdated = null;
        
        logger.info(`Updated game config: ${key} = ${JSON.stringify(value)}`);
    } catch (error) {
        LogErrorStack(`Failed to update config for key: ${key}`, error);
        throw error;
    }
};

/**
 * Delete a config value from the database (will fall back to static config)
 */
export const deleteConfig = async (key: string): Promise<void> => {
    try {
        await GameConfigRepository.deleteConfig(key);

        // Invalidate cache to force refresh on next access
        cacheLastUpdated = null;
        
        logger.info(`Deleted game config: ${key}`);
    } catch (error) {
        LogErrorStack(`Failed to delete config for key: ${key}`, error);
        throw error;
    }
};

/**
 * Reset all config to static defaults
 */
export const resetToDefaults = async (): Promise<void> => {
    try {
        await GameConfigRepository.deleteAllConfigs();

        // Invalidate cache to force refresh on next access
        cacheLastUpdated = null;
        
        logger.info("Reset all game config to static defaults");
    } catch (error) {
        LogErrorStack("Failed to reset config to defaults", error);
        throw error;
    }
};

/**
 * Seed database with static config values
 */
export const seedFromStaticConfig = async (): Promise<void> => {
    try {
        const configEntries: GameConfigRepository.GameConfigCreateInput[] = [];

        // Process each config section
        const sections = [
            { name: "auth", config: staticGameConfig.authConfig },
            { name: "frontend", config: staticGameConfig.frontendConfig },
            { name: "battle", config: staticGameConfig.battleConfig },
            { name: "bank", config: staticGameConfig.bankConfig },
            { name: "chat", config: staticGameConfig.chatConfig },
            { name: "crafting", config: staticGameConfig.craftingConfig },
            { name: "casino", config: staticGameConfig.casinoConfig },
            { name: "user", config: staticGameConfig.userConfig },
            { name: "jobs", config: staticGameConfig.jobsConfig },
            { name: "notification", config: staticGameConfig.notificationConfig },
            { name: "levelGates", config: staticGameConfig.levelGatesConfig },
            { name: "leaderboards", config: staticGameConfig.leaderboardsConfig },
            { name: "roguelike", config: staticGameConfig.roguelikeConfig },
            { name: "uniqueItems", config: staticGameConfig.uniqueItemsConfig },
            { name: "classes", config: staticGameConfig.classesConfig },
            { name: "registrationCodes", config: staticGameConfig.registrationCodes },
            { name: "quests", config: staticGameConfig.questsConfig },
            { name: "shop", config: staticGameConfig.shopConfig },
            { name: "skills", config: staticGameConfig.skillsConfig },
            { name: "bounty", config: staticGameConfig.bountyConfig },
            { name: "profile", config: staticGameConfig.profileConfig },
            { name: "mission", config: staticGameConfig.missionConfig },
            { name: "shrine", config: staticGameConfig.shrineConfig },
            { name: "auction", config: staticGameConfig.auctionConfig },
            { name: "gang", config: staticGameConfig.gangConfig },
            { name: "infirmary", config: staticGameConfig.infirmaryConfig },
            { name: "pet", config: staticGameConfig.petConfig },
        ];

        // Add version as a special config entry
        configEntries.push({
            key: "version",
            value: staticGameConfig.version,
            section: "system",
            isPublic: true,
            description: "Game configuration version",
            version: staticGameConfig.version,
        });

        // Process each section
        for (const section of sections) {
            const { public: publicConfig, hidden: hiddenConfig } = section.config;

            // Add public config entries
            for (const [key, value] of Object.entries(publicConfig)) {
                configEntries.push({
                    key,
                    value,
                    section: section.name,
                    isPublic: true,
                    description: `Public ${section.name} configuration`,
                    version: staticGameConfig.version,
                });
            }

            // Add hidden config entries
            for (const [key, value] of Object.entries(hiddenConfig)) {
                configEntries.push({
                    key,
                    value,
                    section: section.name,
                    isPublic: false,
                    description: `Hidden ${section.name} configuration`,
                    version: staticGameConfig.version,
                });
            }
        }

        await GameConfigRepository.bulkUpsertConfigs(configEntries);

        // Invalidate cache to force refresh on next access
        cacheLastUpdated = null;
        
        logger.info(`Seeded database with ${configEntries.length} config entries`);
    } catch (error) {
        LogErrorStack("Failed to seed config from static config", error);
        throw error;
    }
};

/**
 * Force cache refresh (useful for testing or admin operations)
 */
export const invalidateCache = (): void => {
    cacheLastUpdated = null;
    configCache.clear();
    publicConfigCache.clear();
};
