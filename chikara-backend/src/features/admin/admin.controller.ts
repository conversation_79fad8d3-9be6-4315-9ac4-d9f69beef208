import { gameConfig } from "../../config/gameConfig.js";
import * as GameConfigService from "../../core/gameconfig.service.js";
import * as EquipmentService from "../../core/equipment.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as UserService from "../../core/user.service.js";
import * as SkillsService from "../../core/skills.service.js";
import {
    findAndValidateUser,
    findInactivePlayers,
    findItem,
    getActiveUsersInTimeWindow,
    getCirculatingYen,
    getUsersInDateRange,
    logAdminAction,
    reviveUser,
    updateUserAccountDetails,
    updateUserBan,
} from "./admin.helpers.js";
import * as adminRepository from "../../repositories/admin.repository.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { listBattles } from "../battle/battle.state.js";
import * as ChatHelper from "../chat/chat.helpers.js";
import { MapType } from "../roguelike/roguelike.types.js";
import { getLatestUserActions } from "../../lib/actionLogger.js";
import { ItemModel, db } from "../../lib/db.js";
import { maintenanceState } from "../../middleware/maintenanceMiddleware.js";
import { NotificationTypes } from "../../types/notification.js";
import { getNow } from "../../utils/dateHelpers.js";
import { LogErrorStack } from "../../utils/log.js";
import { Prisma, type SkillType } from "@prisma/client";

export const getBattlesList = async () => {
    const battles = await listBattles();
    return { data: battles };
};

export const getLatestLogs = async (userId: number) => {
    const logs = await getLatestUserActions(String(userId));
    return { data: logs };
};

export const ChatBanUser = async (userId: number, timeToBanMS: number, adminId: number) => {
    const userToBan = await findAndValidateUser(userId);
    await updateUserBan(userToBan, adminId, "chatBannedUntil", timeToBanMS, "Chat");
    return { statusCode: 200 };
};

export const RemoveUserChatMessages = async (userId: number, adminId: number) => {
    const user = await findAndValidateUser(userId);

    await adminRepository.hideAllUserChatMessages(user.id);
    NotificationService.NotifyMessageRemoved();

    await logAdminAction(adminId, "REMOVE_USER_CHAT_MESSAGES", {
        username: user.username,
        userId: userId,
    });

    return { statusCode: 200 };
};

export const HideSingleChatMessage = async (messageId: number, adminId: number) => {
    if (!messageId) {
        return { error: "No message id!", statusCode: 400 };
    }

    await adminRepository.updateChatMessageVisibility(messageId, true);
    NotificationService.NotifyMessageRemoved();

    await logAdminAction(adminId, "HIDE_CHAT_MESSAGE", {
        messageId: messageId,
    });

    return { statusCode: 200 };
};

export const UnhideSingleChatMessage = async (messageId: number, adminId: number) => {
    if (!messageId) {
        return { error: "No message id!", statusCode: 400 };
    }

    await adminRepository.updateChatMessageVisibility(messageId, false);
    NotificationService.NotifyMessageRemoved();

    await logAdminAction(adminId, "UNHIDE_CHAT_MESSAGE", {
        messageId: messageId,
    });

    return { statusCode: 200 };
};

export const DeleteSingleChatMessage = async (messageId: number, adminId: number) => {
    if (!messageId) {
        return { error: "No message id!", statusCode: 400 };
    }

    await adminRepository.deleteChatMessage(messageId);
    NotificationService.NotifyMessageRemoved();

    await logAdminAction(adminId, "DELETE_CHAT_MESSAGE", {
        messageId: messageId,
    });

    return { statusCode: 200 };
};

export const BanUser = async (userId: number, timeToBanMS: number, adminId: number) => {
    const userToBan = await findAndValidateUser(userId);
    await updateUserBan(userToBan, adminId, "banExpires", timeToBanMS, "Account");

    NotificationService.NotifyUser(userId, NotificationTypes.banned, { banExpires: userToBan.banExpires });

    await logAdminAction(adminId, "BAN_USER", {
        username: userToBan.username,
        userId: userId,
    });

    return { statusCode: 200 };
};

export const GiveItem = async (
    itemName: string,
    amount = 1,
    userId: number,
    adminId: number,
    message: string | null = null
) => {
    let itemId = null;

    if (itemName) {
        const itemByName = await ItemRepository.findItemByName(itemName);
        itemId = itemByName?.id || null;
    }

    if (!itemId) {
        return { error: "Item not found", statusCode: 400 };
    }

    const item = itemId ? await ItemRepository.findItemById(itemId) : null;
    if (!item) {
        return { error: "Item not found", statusCode: 400 };
    }

    await findAndValidateUser(userId);

    await InventoryService.AddItemToUser({
        userId,
        itemId,
        amount,
        isTradeable: false,
    });
    NotificationService.NotifyUser(userId, NotificationTypes.admin_action, {
        senderId: adminId,
        type: "item_gift",
        item,
        amount,
        message,
    });

    await logAdminAction(adminId, "GIVE_ITEM", {
        itemId: itemId,
        amount: amount,
        userId: userId,
    });

    return { statusCode: 200 };
};

export const RemoveItem = async (
    itemName: string,
    amount = 1,
    userId: number,
    adminId: number,
    itemId: number | null = null
) => {
    const item = await findItem(itemName, itemId);
    if (!item) {
        return { error: "Item not found", statusCode: 400 };
    }

    const itemIdToRemove = item.id;

    await findAndValidateUser(userId);
    await InventoryService.SubtractItemFromUser({ userId, itemId: itemIdToRemove, amount });
    await logAdminAction(adminId, "REMOVE_ITEM", {
        itemId: itemIdToRemove,
        amount: amount,
        userId: userId,
    });

    return { statusCode: 200 };
};

export const ReviveUser = async (userId: number, adminId: number) => {
    const user = await findAndValidateUser(userId);

    await reviveUser(user);
    await logAdminAction(adminId, "REVIVE_USER", {
        username: user.username,
        userId: userId,
    });

    return { statusCode: 200 };
};

export const GetFullUserInfo = async (userId: number) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }

    return { data: user };
};

export const GetFullGangInfo = async (gangId: number) => {
    const gang = await adminRepository.getFullGangInfo(gangId);

    if (!gang) {
        return { error: "Gang not found", statusCode: 404 };
    }

    return { data: gang };
};

export const GetFullGameConfig = async () => {
    try {
        const config = await GameConfigService.getAllConfig();
        return { data: config };
    } catch (error) {
        return { error: "Failed to get game config", statusCode: 500 };
    }
};

export const ToggleMaintenanceMode = async (enabled: boolean, adminId: number) => {
    // Update the maintenance mode setting in memory
    // NOTE: This will reset when server restarts

    maintenanceState.isEnabled = enabled;

    await logAdminAction(adminId, "TOGGLE_MAINTENANCE_MODE", {
        enabled: String(enabled),
    });

    return {
        statusCode: 200,
        data: {
            success: true,
            message: `Maintenance mode ${enabled ? "enabled" : "disabled"}`,
            maintenanceModeEnabled: enabled,
        },
    };
};

export const GetUserEquippedValues = async (userId: number) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        return { error: "User not found", statusCode: 404 };
    }

    const totalArmour = await EquipmentService.GetTotalEquippedValue(user, "armour");
    const totalDamage = await EquipmentService.GetTotalEquippedValue(user, "damage");
    // const totalStrength = await EquipmentService.GetTotalEquippedValue(user, "strength");
    // const totalDexterity = await EquipmentService.GetTotalEquippedValue(user, "dexterity");
    const equippedItems = await EquipmentService.GetEquippedItems(user.id);

    const equippedValues = {
        totalArmour,
        totalDamage,
        // totalStrength,
        // totalDexterity,
        equippedItems,
    };
    return { data: equippedValues };
};

export const ResetUserRoguelikeData = async (userId: number, adminId: number) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }

    await UserService.updateUser(userId, {
        roguelikeMap: Prisma.DbNull,
    });

    await logAdminAction(adminId, "RESET_USER_ROGUELIKE_DATA", {
        username: user.username,
        userId: userId,
    });
    return { statusCode: 200 };
};

export const UpdateUserRoguelikeData = async (userId: number, mapdata: MapType, level: number, adminId: number) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }

    const updateData: Prisma.userUpdateInput = {};
    if (mapdata && mapdata instanceof Object) {
        updateData.roguelikeMap = mapdata;
    }

    if (level) {
        updateData.roguelikeLevel = level;
    }

    await UserService.updateUser(user.id, updateData);

    await logAdminAction(adminId, "UPDATE_USER_ROGUELIKE_DATA", {
        username: user.username,
        userId: userId,
        mapdata: JSON.stringify(mapdata),
        level: level,
    });
    return { statusCode: 200 };
};

export const UpdateUserRoguelikeBuffs = async (userId: number, strBuff: number, defBuff: number, adminId: number) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }
    if (!user.roguelikeMap) {
        return { error: "User has no roguelike map", statusCode: 400 };
    }
    const map = user.roguelikeMap;
    if (strBuff) {
        map.strBuff = strBuff;
    }
    if (defBuff) {
        map.defBuff = defBuff;
    }

    await UserService.updateUser(user.id, { roguelikeMap: map });

    await logAdminAction(adminId, "UPDATE_USER_ROGUELIKE_BUFFS", {
        username: user.username,
        userId: userId,
        strBuff: strBuff,
        defBuff: defBuff,
    });
    return { statusCode: 200 };
};

export const JailUser = async (userId: number, timeToJailMS: number, jailReason: string, adminId: number) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }

    const userData = await UserService.JailUser(userId, timeToJailMS, jailReason, {
        notificationType: NotificationTypes.jail,
    });

    await logAdminAction(adminId, "JAIL_USER", {
        username: user.username,
        userId: userId,
        until: String(new Date(Number(userData.jailedUntil))),
    });

    return { statusCode: 200 };
};

export const BailUser = async (userId: number, adminId: number) => {
    const user = await findAndValidateUser(userId);

    await UserService.updateUser(userId, {
        jailedUntil: null,
        jailReason: null,
    });
    await logAdminAction(adminId, "BAIL_USER", {
        username: user.username,
        userId: userId,
    });

    return { statusCode: 200 };
};

export const UpdateUserValues = async (userId: number, type: string, value: number, adminId: number) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }
    const updateData: Prisma.userUpdateInput = {};
    switch (type) {
        case "health":
        case "hp": {
            updateData.currentHealth = value;
            break;
        }
        case "energy": {
            updateData.energy = value;
            break;
        }
        case "actionpoints":
        case "ap": {
            updateData.actionPoints = value;
            break;
        }
        case "level": {
            updateData.level = value;
            break;
        }
        case "exp":
        case "xp": {
            await UserService.AddXPToUser(user, value);
            // AddXPToUser already saves the user
            await logAdminAction(adminId, "UPDATE_USER_VALUES", {
                username: user.username,
                userId: userId,
                type: type,
                value: value,
            });
            return { statusCode: 200 };
        }
        default: {
            return { error: "Invalid type", statusCode: 400 };
        }
    }

    await UserService.updateUser(user.id, updateData);
    await logAdminAction(adminId, "UPDATE_USER_VALUES", {
        username: user.username,
        userId: userId,
        type: type,
        value: value,
    });

    return { statusCode: 200 };
};

export const UpdateUserMoney = async (userId: number, method: string, type: string, value: number, adminId: number) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }
    const updateData: Prisma.userUpdateInput = {};
    if (type === "cash") {
        if (method === "add") {
            updateData.cash = { increment: value };
        }
        if (method === "subtract") {
            updateData.cash = { decrement: value };
        }
        if (method === "update") {
            updateData.cash = value;
        }
    }
    if (type === "bankBalance") {
        if (method === "add") {
            updateData.bank_balance = { increment: value };
        }
        if (method === "subtract") {
            updateData.bank_balance = { decrement: value };
        }
        if (method === "update") {
            updateData.bank_balance = value;
        }
    }

    await UserService.updateUser(user.id, updateData);
    await logAdminAction(adminId, "UPDATE_USER_MONEY", {
        username: user.username,
        userId: userId,
        type: type,
        method: method,
        value: value,
    });
    return { statusCode: 200 };
};

export const UpdateUserStats = async (
    userId: number,
    targetStat: string,
    value: number,
    method: string,
    adminId: number
) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }

    // Check if the stat is a combat stat that should use the skill system
    const combatStats = ["strength", "defence", "intelligence", "dexterity", "endurance", "vitality"];

    if (combatStats.includes(targetStat)) {
        const skillType = targetStat as SkillType;

        // Get current skill level
        const currentSkill = await SkillsService.getUserSkill(userId, skillType);
        let newLevel = currentSkill.level;

        switch (method) {
            case "add": {
                newLevel = currentSkill.level + value;
                break;
            }
            case "subtract": {
                newLevel = Math.max(1, currentSkill.level - value); // Don't go below level 1
                break;
            }
            case "update": {
                newLevel = Math.max(1, value); // Don't go below level 1
                break;
            }
            default: {
                return { error: "Invalid method", statusCode: 400 };
            }
        }

        // Update the skill level directly in the database
        await db.user_skill.upsert({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
            update: {
                level: newLevel,
                experience: 0, // Reset experience when admin sets level
                updatedAt: new Date(),
            },
            create: {
                userId,
                skillType,
                level: newLevel,
                experience: 0,
                talentPoints: 0,
            },
        });
    } else {
        return { error: "Invalid stat type", statusCode: 400 };
    }

    await logAdminAction(adminId, "UPDATE_USER_STATS", {
        username: user.username,
        userId: userId,
        targetStat: targetStat,
        value: value,
        method: method,
    });

    return { statusCode: 200 };
};

export const UpdateAdminNotes = async (userId: number, notes: string, adminId: number) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }

    await UserService.updateUser(userId, {
        adminNotes: notes,
    });

    await logAdminAction(adminId, "UPDATE_USER_ADMIN_NOTES", {
        username: user.username,
        userId: userId,
        notes: notes,
    });
    return { statusCode: 200 };
};

export const UpdateUserAvatar = async (userId: number, newAvatar: string, adminId: number) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "Could not find user", statusCode: 400 };
    }
    if (!newAvatar) {
        return { error: "Input a new avatar", statusCode: 400 };
    }

    await UserService.updateUser(userId, {
        avatar: newAvatar,
    });

    await logAdminAction(adminId, "UPDATE_USER_AVATAR", {
        username: user.username,
        userId: userId,
    });
    return { statusCode: 200 };
};

export const UpdateUserAccountDetails = async (
    userId: number,
    updates: {
        password?: string;
        username?: string;
        email?: string;
        avatar?: string;
    },
    files: {
        avatar?: Express.Multer.File[];
        banner?: Express.Multer.File[];
    },
    adminId: number
) => {
    const user = await findAndValidateUser(userId);

    try {
        await updateUserAccountDetails(user, updates, files);
        await logAdminAction(adminId, "UPDATE_USER_ACCOUNT_DETAILS", {
            username: user.username,
            userId: userId,
        });

        return { statusCode: 200 };
    } catch (error: unknown) {
        if (error instanceof Error) {
            return { error: error.message, statusCode: 400 };
        }
        return { error: "An unknown error occurred", statusCode: 400 };
    }
};

export const ActiveUsersStats = async (startDate: Date, endDate: Date) => {
    if (!startDate || !endDate) {
        return { error: "Missing date parameters", statusCode: 400 };
    }

    const count = await getUsersInDateRange("last_activity", startDate, endDate);
    return { data: { amount: count } };
};

export const RegistrationStats = async (startDate: Date, endDate: Date) => {
    if (!startDate || !endDate) {
        return { error: "Missing date parameters", statusCode: 400 };
    }

    const count = await getUsersInDateRange("createdAt", startDate, endDate);
    return { data: { amount: count } };
};

export const GetCirculatingYenThisWeek = async () => {
    const totalYen = await getCirculatingYen(7);
    return { data: totalYen };
};

export const CurrentActiveUsers = async () => {
    const count = await getActiveUsersInTimeWindow(10);
    return { data: { amount: count } };
};

export const TotalUsers = async () => {
    const count = await adminRepository.countTotalUsers();
    return { data: { amount: count } };
};

export const CreateTestUser = async () => {
    const id = (await adminRepository.countTotalUsers()) + 1;
    const userData = {
        email: `testuser${id}@test.com`,
        username: `testuser${id}`,
        password: `$2b$08$5r04Kqx3ES0yBEaN.fNzhejxQ7rW4bsloRTVwhtQHMPKk/bV.RNPm`, // testtest123
        firstname: `testuser${id}`,
        lastname: `testuser${id}`,
        class: "Honoo",
        adminNotes: "testUser",
    };

    const newUser = await adminRepository.createTestUser(userData);
    if (!newUser) {
        return { error: "Failed to create test user", statusCode: 400 };
    }
    return { data: newUser };
};

export const GetFullUserList = async () => {
    const attributes = [
        "id",
        "username",
        "avatar",
        "email",
        "userType",
        "last_activity",
        "level",
        "cash",
        "bank_balance",
        "combatLevel",
        "createdAt",
    ];

    const users = await adminRepository.getAllUsersWithAttributes(attributes);
    return { data: users };
};

export const SendPatchNotesGlobalNotification = async (id: number) => {
    if (!id) {
        return { error: "Needs patch note ID", statusCode: 400 };
    }

    await ChatHelper.SendAnnouncementMessage("patchNotes", JSON.stringify({ id }));

    return { statusCode: 200 };
};

// TODO: Trigger the bullmq job to end the lottery
export const EndLottery = async () => {
    return { data: "Lottery ended" };
};

// ===============================================
// Game Config Management
// ===============================================

export const UpdateGameConfig = async (
    key: string,
    value: unknown,
    section?: string,
    isPublic?: boolean,
    description?: string
) => {
    try {
        await GameConfigService.updateConfig(key, value, section, isPublic, description);
        return { data: "Config updated successfully" };
    } catch (error) {
        return { error: "Failed to update config", statusCode: 500 };
    }
};

export const DeleteGameConfig = async (key: string) => {
    try {
        await GameConfigService.deleteConfig(key);
        return { data: "Config deleted successfully" };
    } catch (error) {
        return { error: "Failed to delete config", statusCode: 500 };
    }
};

export const ResetGameConfigToDefaults = async () => {
    try {
        await GameConfigService.resetToDefaults();
        return { data: "Config reset to defaults successfully" };
    } catch (error) {
        return { error: "Failed to reset config", statusCode: 500 };
    }
};

export const SeedGameConfigFromStatic = async () => {
    try {
        await GameConfigService.seedFromStaticConfig();
        return { data: "Config seeded from static config successfully" };
    } catch (error) {
        return { error: "Failed to seed config", statusCode: 500 };
    }
};

export const FindQuitPlayers = async () => {
    const players = await findInactivePlayers(3, 20);
    return { data: players };
};

export const ProfileDetailsChangeBan = async (userId: number, timeToBanMS: number, adminId: number) => {
    const userToBan = await findAndValidateUser(userId);

    await UserService.updateUser(userId, {
        profileDetailBanUntil: BigInt(Date.now() + timeToBanMS),
    });

    await logAdminAction(adminId, "PROFILE_DETAIL_BAN", {
        username: userToBan.username,
        userId: userId,
        until: String(userToBan.profileDetailBanUntil),
    });

    return { statusCode: 200 };
};

export const CreateAuctionListing = async (itemId: number, adminId: number) => {
    if (!itemId) {
        return { error: "Missing item id", statusCode: 400 };
    }
    const currentTime = getNow();
    const endsAt = new Date(currentTime.setMinutes(currentTime.getMinutes() + 1));

    const auctionItem = await adminRepository.createAuctionItem({
        item: { connect: { id: itemId } },
        user: { connect: { id: adminId } },
        quantity: 1,
        deposit: 50,
        buyoutPrice: 100,
        endsAt,
        bankFunds: false,
        status: "in_progress",
    });

    return { data: auctionItem };
};

export const SendTestPushNotification = async (message = "Test push notification", userId: number) => {
    const response = await NotificationService.sendSinglePushNotification(userId, message);
    return { data: response };
};

export const bulkCreateItems = async (items: ItemModel[]) => {
    const itemsToCreate = (items || []).map((item) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { id, createdAt, updatedAt, ...itemData } = item;
        return {
            ...itemData,
            itemEffects: item.itemEffects ?? Prisma.DbNull,
        };
    });
    await ItemRepository.bulkCreateItems(itemsToCreate);
    return { data: itemsToCreate };
};

export const resetAllUserRoguelikeMaps = async () => {
    await adminRepository.resetAllUserRoguelikeMaps();
    return { data: "Complete" };
};

export const ManualGangPayout = async (gangId: number, payoutAmount: number, adminId: number) => {
    const parsedGangId = Number(gangId);
    const parsedPayoutAmount = Number(payoutAmount);

    const gangs = await adminRepository.findGangsWithMembers(parsedGangId);
    if (!gangs || gangs.length === 0) {
        return { error: "Gang not found", statusCode: 400 };
    }

    const gang = gangs[0];
    const members = gang.gang_member;
    const memberPayout = Math.round(parsedPayoutAmount / members.length);

    for (const member of members) {
        const userId = member.userId;
        if (!userId) {
            continue;
        }
        const user = await UserRepository.getUserById(userId);

        if (user) {
            try {
                await adminRepository.incrementUserGangCredits(userId, memberPayout);

                NotificationService.NotifyUser(userId, NotificationTypes.temporary_notification, {
                    manualPayout: true,
                    amount: memberPayout,
                });

                await logAdminAction(adminId, "MANUAL_GANG_PAYOUT", {
                    username: user.username,
                    userId: userId,
                    amount: memberPayout,
                    gangId: parsedGangId,
                });
            } catch (error) {
                LogErrorStack({ error });
            }
        }
    }

    return { data: "Complete" };
};

export const CreateAnnouncementMessage = async (message: string) => {
    if (!message) {
        return { error: "Invalid message", statusCode: 400 };
    }

    await ChatHelper.SendAnnouncementMessage("consoleMessage", message);

    return { statusCode: 200 };
};

export default {
    getBattlesList,
    getLatestLogs,
    ChatBanUser,
    RemoveUserChatMessages,
    HideSingleChatMessage,
    UnhideSingleChatMessage,
    DeleteSingleChatMessage,
    BanUser,
    GiveItem,
    RemoveItem,
    ReviveUser,
    GetFullUserInfo,
    GetFullGangInfo,
    GetUserEquippedValues,
    ResetUserRoguelikeData,
    UpdateUserRoguelikeData,
    UpdateUserRoguelikeBuffs,
    JailUser,
    BailUser,
    UpdateUserValues,
    UpdateUserMoney,
    UpdateUserStats,
    UpdateAdminNotes,
    ActiveUsersStats,
    ManualGangPayout,
    CreateAnnouncementMessage,
    EndLottery,
    FindQuitPlayers,
    ProfileDetailsChangeBan,
    CreateAuctionListing,
    SendTestPushNotification,
    bulkCreateItems,
    resetAllUserRoguelikeMaps,
    ToggleMaintenanceMode,
};
